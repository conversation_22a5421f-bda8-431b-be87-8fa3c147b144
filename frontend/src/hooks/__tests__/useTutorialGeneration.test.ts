import { renderHook, act } from '@testing-library/react'
import { useTutorialGeneration } from '../useTutorialGeneration'
import { apiService, ApiError, NetworkError, TimeoutError } from '../../services/api'

// Mock the API service
jest.mock('../../services/api')
const mockApiService = apiService as jest.Mocked<typeof apiService>

describe('useTutorialGeneration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useTutorialGeneration())

    expect(result.current.isGenerating).toBe(false)
    expect(result.current.error).toBe(null)
    expect(result.current.tutorialPlan).toBe(null)
    expect(result.current.videoId).toBe(null)
    expect(result.current.steps).toHaveLength(4)
    expect(result.current.retryCount).toBe(0)
    expect(result.current.maxRetries).toBe(3)
  })

  it('should handle validation errors', async () => {
    const { result } = renderHook(() => useTutorialGeneration())

    await act(async () => {
      try {
        await result.current.generateTutorial('')
      } catch (error) {
        // Expected to throw
      }
    })

    expect(result.current.error).toBeTruthy()
    expect(result.current.error?.type).toBe('validation')
    expect(result.current.error?.message).toContain('required')
    expect(result.current.error?.retryable).toBe(false)
  })

  it('should handle network errors with retry capability', async () => {
    const { result } = renderHook(() => useTutorialGeneration())
    
    mockApiService.analyzeProblem.mockRejectedValue(new NetworkError('Connection failed'))

    await act(async () => {
      try {
        await result.current.generateTutorial('Valid problem description')
      } catch (error) {
        // Expected to throw after retries
      }
    })

    expect(result.current.error).toBeTruthy()
    expect(result.current.error?.type).toBe('network')
    expect(result.current.error?.retryable).toBe(true)
    expect(mockApiService.analyzeProblem).toHaveBeenCalledTimes(4) // Initial + 3 retries
  })

  it('should handle API errors correctly', async () => {
    const { result } = renderHook(() => useTutorialGeneration())
    
    mockApiService.analyzeProblem.mockRejectedValue(new ApiError('Server error', 500, 'INTERNAL_ERROR'))

    await act(async () => {
      try {
        await result.current.generateTutorial('Valid problem description')
      } catch (error) {
        // Expected to throw after retries
      }
    })

    expect(result.current.error).toBeTruthy()
    expect(result.current.error?.type).toBe('api')
    expect(result.current.error?.code).toBe('INTERNAL_ERROR')
    expect(result.current.error?.retryable).toBe(true) // 500 errors are retryable
  })

  it('should handle timeout errors', async () => {
    const { result } = renderHook(() => useTutorialGeneration())
    
    mockApiService.analyzeProblem.mockRejectedValue(new TimeoutError('Request timed out'))

    await act(async () => {
      try {
        await result.current.generateTutorial('Valid problem description')
      } catch (error) {
        // Expected to throw after retries
      }
    })

    expect(result.current.error).toBeTruthy()
    expect(result.current.error?.type).toBe('timeout')
    expect(result.current.error?.retryable).toBe(true)
  })

  it('should successfully generate tutorial', async () => {
    const { result } = renderHook(() => useTutorialGeneration())
    
    const mockTutorialPlan = {
      title: 'Test Tutorial',
      difficulty: 'beginner' as const,
      estimatedTime: '10 minutes',
      technologies: ['JavaScript'],
      steps: [
        {
          stepNumber: 1,
          title: 'Step 1',
          description: 'First step',
          uiActions: ['click'],
          expectedResult: 'Result'
        }
      ],
      prerequisites: [],
      resources: []
    }

    const mockVideoStatus = {
      videoId: 'test-video-id',
      status: 'completed' as const,
      progress: 100,
      downloadUrl: 'http://example.com/video.mp4',
      updatedAt: new Date().toISOString()
    }

    mockApiService.analyzeProblem.mockResolvedValue({
      success: true,
      tutorialPlan: mockTutorialPlan,
      metadata: {
        generatedAt: new Date().toISOString(),
        model: 'test-model',
        originalProblem: 'test problem'
      }
    })

    mockApiService.generateVideo.mockResolvedValue({
      success: true,
      videoId: 'test-video-id',
      status: 'processing',
      estimatedCompletionTime: '2 minutes',
      message: 'Video generation started',
      metadata: {
        title: 'Test Tutorial',
        duration: '10 minutes',
        steps: 1,
        createdAt: new Date().toISOString(),
        difficulty: 'beginner',
        technologies: ['JavaScript']
      }
    })

    mockApiService.getVideoStatus.mockResolvedValue(mockVideoStatus)

    await act(async () => {
      await result.current.generateTutorial('Valid problem description')
    })

    expect(result.current.isGenerating).toBe(false)
    expect(result.current.error).toBe(null)
    expect(result.current.tutorialPlan).toEqual(mockTutorialPlan)
    expect(result.current.videoId).toBe('test-video-id')
    expect(result.current.videoStatus).toEqual(mockVideoStatus)
    expect(result.current.isCompleted).toBe(true)
  })

  it('should clear error state', () => {
    const { result } = renderHook(() => useTutorialGeneration())

    // Set an error state first
    act(() => {
      result.current.generateTutorial('') // This will set a validation error
    })

    expect(result.current.error).toBeTruthy()

    // Clear the error
    act(() => {
      result.current.clearError()
    })

    expect(result.current.error).toBe(null)
  })

  it('should reset state correctly', () => {
    const { result } = renderHook(() => useTutorialGeneration())

    // Set some state first
    act(() => {
      result.current.generateTutorial('') // This will set a validation error
    })

    expect(result.current.error).toBeTruthy()

    // Reset state
    act(() => {
      result.current.resetState()
    })

    expect(result.current.isGenerating).toBe(false)
    expect(result.current.error).toBe(null)
    expect(result.current.tutorialPlan).toBe(null)
    expect(result.current.videoId).toBe(null)
    expect(result.current.retryCount).toBe(0)
  })
})
