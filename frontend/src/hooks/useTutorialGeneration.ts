import React, { useState, useCallback, useRef } from 'react'
import { apiService, ApiError, NetworkError, TimeoutError } from '../services/api'
import type { TutorialPlan, VideoStatus } from '../services/api'

interface ProgressStep {
  id: string
  title: string
  status: 'pending' | 'in-progress' | 'completed' | 'error'
  description?: string
  error?: string
}

interface ErrorDetails {
  message: string
  type: 'network' | 'timeout' | 'api' | 'validation' | 'unknown'
  code?: string
  step?: string
  retryable: boolean
  timestamp: Date
}

interface TutorialGenerationState {
  // Current state
  isGenerating: boolean
  currentStep: number
  error: ErrorDetails | null

  // Data
  tutorialPlan: TutorialPlan | null
  videoId: string | null
  videoStatus: VideoStatus | null

  // Progress tracking
  steps: ProgressStep[]

  // Retry state
  retryCount: number
  maxRetries: number
}

const defaultSteps: ProgressStep[] = [
  {
    id: '1',
    title: 'Analyzing Problem',
    status: 'pending',
    description: 'AI is understanding your coding challenge'
  },
  {
    id: '2',
    title: 'Generating Steps',
    status: 'pending',
    description: 'Creating step-by-step solution plan'
  },
  {
    id: '3',
    title: 'Creating Animation',
    status: 'pending',
    description: 'Building animated tutorial video'
  },
  {
    id: '4',
    title: 'Rendering Video',
    status: 'pending',
    description: 'Finalizing your tutorial'
  }
]

export const useTutorialGeneration = () => {
  const [state, setState] = useState<TutorialGenerationState>({
    isGenerating: false,
    currentStep: 0,
    error: null,
    tutorialPlan: null,
    videoId: null,
    videoStatus: null,
    steps: [...defaultSteps],
    retryCount: 0,
    maxRetries: 3
  })

  // Use ref to track if component is mounted to prevent state updates after unmount
  const isMountedRef = useRef(true)

  // Helper function to create error details
  const createErrorDetails = (error: unknown, step?: string): ErrorDetails => {
    let errorDetails: ErrorDetails = {
      message: 'Unknown error occurred',
      type: 'unknown',
      retryable: false,
      timestamp: new Date()
    }

    if (error instanceof NetworkError) {
      errorDetails = {
        message: error.message,
        type: 'network',
        retryable: true,
        timestamp: new Date(),
        step
      }
    } else if (error instanceof TimeoutError) {
      errorDetails = {
        message: error.message,
        type: 'timeout',
        retryable: true,
        timestamp: new Date(),
        step
      }
    } else if (error instanceof ApiError) {
      errorDetails = {
        message: error.message,
        type: 'api',
        code: error.code,
        retryable: error.status ? error.status >= 500 : false,
        timestamp: new Date(),
        step
      }
    } else if (error instanceof Error) {
      // Check for validation errors
      if (error.message.includes('validation') || error.message.includes('invalid')) {
        errorDetails = {
          message: error.message,
          type: 'validation',
          retryable: false,
          timestamp: new Date(),
          step
        }
      } else {
        errorDetails = {
          message: error.message,
          type: 'unknown',
          retryable: false,
          timestamp: new Date(),
          step
        }
      }
    }

    return errorDetails
  }

  const updateStep = useCallback((stepIndex: number, status: ProgressStep['status'], error?: string) => {
    if (!isMountedRef.current) return

    setState(prev => ({
      ...prev,
      steps: prev.steps.map((step, index) =>
        index === stepIndex ? { ...step, status, error } : step
      ),
      currentStep: stepIndex
    }))
  }, [])

  const resetState = useCallback(() => {
    if (!isMountedRef.current) return

    setState({
      isGenerating: false,
      currentStep: 0,
      error: null,
      tutorialPlan: null,
      videoId: null,
      videoStatus: null,
      steps: [...defaultSteps],
      retryCount: 0,
      maxRetries: 3
    })
  }, [])

  // Helper function to handle retryable operations
  const withRetry = useCallback(async <T>(
    operation: () => Promise<T>,
    stepName: string,
    stepIndex: number
  ): Promise<T> => {
    let lastError: unknown

    for (let attempt = 0; attempt <= state.maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          // Wait before retry with exponential backoff
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000)
          await new Promise(resolve => setTimeout(resolve, delay))

          if (!isMountedRef.current) throw new Error('Component unmounted')

          // Update retry count
          setState(prev => ({ ...prev, retryCount: attempt }))
        }

        return await operation()
      } catch (error) {
        lastError = error
        const errorDetails = createErrorDetails(error, stepName)

        // If it's not retryable or we've exhausted retries, throw
        if (!errorDetails.retryable || attempt === state.maxRetries) {
          updateStep(stepIndex, 'error', errorDetails.message)
          throw error
        }

        // Log retry attempt
        console.warn(`Attempt ${attempt + 1} failed for ${stepName}, retrying...`, error)
      }
    }

    throw lastError
  }, [state.maxRetries, createErrorDetails, updateStep])

  const generateTutorial = useCallback(async (problem: string) => {
    // Validation
    if (!problem || problem.trim().length === 0) {
      const error = createErrorDetails(new Error('Problem description is required'), 'validation')
      setState(prev => ({ ...prev, error }))
      throw new Error('Problem description is required')
    }

    if (problem.trim().length < 10) {
      const error = createErrorDetails(new Error('Problem description is too short. Please provide more details.'), 'validation')
      setState(prev => ({ ...prev, error }))
      throw new Error('Problem description is too short. Please provide more details.')
    }

    try {
      // Reset state and start generation
      if (!isMountedRef.current) return

      setState(prev => ({
        ...prev,
        isGenerating: true,
        error: null,
        steps: [...defaultSteps],
        retryCount: 0
      }))

      // Step 1: Analyze problem with retry logic
      updateStep(0, 'in-progress')

      const analyzeResponse = await withRetry(
        () => apiService.analyzeProblem(problem),
        'Problem Analysis',
        0
      )

      if (!analyzeResponse.success) {
        throw new ApiError('Failed to analyze problem: Invalid response from server')
      }

      if (!analyzeResponse.tutorialPlan || !analyzeResponse.tutorialPlan.steps || analyzeResponse.tutorialPlan.steps.length === 0) {
        throw new ApiError('Failed to analyze problem: No tutorial steps generated')
      }

      updateStep(0, 'completed')
      updateStep(1, 'completed') // Steps are generated as part of analysis

      if (!isMountedRef.current) return
      setState(prev => ({
        ...prev,
        tutorialPlan: analyzeResponse.tutorialPlan
      }))

      // Step 2: Generate video with retry logic
      updateStep(2, 'in-progress')

      const videoResponse = await withRetry(
        () => apiService.generateVideo(analyzeResponse.tutorialPlan),
        'Video Generation',
        2
      )

      if (!videoResponse.success || !videoResponse.videoId) {
        throw new ApiError('Failed to start video generation: Invalid response from server')
      }

      if (!isMountedRef.current) return
      setState(prev => ({
        ...prev,
        videoId: videoResponse.videoId
      }))

      updateStep(2, 'completed')
      updateStep(3, 'in-progress')

      // Step 3: Poll for video completion with timeout and retry logic
      const pollVideoStatus = async (pollAttempts = 0): Promise<VideoStatus> => {
        const maxPollAttempts = 60 // 2 minutes with 2-second intervals

        if (pollAttempts >= maxPollAttempts) {
          throw new TimeoutError('Video generation timed out after 2 minutes')
        }

        if (!isMountedRef.current) {
          throw new Error('Component unmounted during video generation')
        }

        try {
          const status = await apiService.getVideoStatus(videoResponse.videoId)

          if (!isMountedRef.current) return status

          setState(prev => ({
            ...prev,
            videoStatus: status
          }))

          if (status.status === 'completed') {
            if (!status.downloadUrl) {
              throw new ApiError('Video completed but no download URL provided')
            }
            updateStep(3, 'completed')
            return status
          } else if (status.status === 'error') {
            const errorMsg = status.error || 'Video generation failed'
            throw new ApiError(`Video generation failed: ${errorMsg}`)
          }

          // Continue polling with exponential backoff
          const delay = Math.min(2000 + (pollAttempts * 100), 5000)
          await new Promise(resolve => setTimeout(resolve, delay))
          return pollVideoStatus(pollAttempts + 1)
        } catch (error) {
          if (error instanceof ApiError || error instanceof TimeoutError) {
            throw error
          }
          // For network errors during polling, retry with backoff
          if (pollAttempts < 3) {
            await new Promise(resolve => setTimeout(resolve, 5000))
            return pollVideoStatus(pollAttempts + 1)
          }
          throw error
        }
      }

      const finalStatus = await pollVideoStatus()

      if (!isMountedRef.current) return
      setState(prev => ({
        ...prev,
        isGenerating: false,
        videoStatus: finalStatus
      }))

      return {
        tutorialPlan: analyzeResponse.tutorialPlan,
        videoId: videoResponse.videoId,
        videoStatus: finalStatus
      }

    } catch (error) {
      if (!isMountedRef.current) return

      const errorDetails = createErrorDetails(error)

      setState(prev => ({
        ...prev,
        isGenerating: false,
        error: errorDetails
      }))

      // Log detailed error for debugging
      console.error('Tutorial generation failed:', {
        error,
        errorDetails,
        step: state.currentStep,
        retryCount: state.retryCount
      })

      throw error
    }
  }, [updateStep, withRetry, createErrorDetails, state.currentStep, state.retryCount])

  const downloadVideo = useCallback(async () => {
    if (!state.videoId) {
      const error = createErrorDetails(new Error('No video available for download'), 'download')
      setState(prev => ({ ...prev, error }))
      throw new Error('No video available for download')
    }

    if (!state.videoStatus || state.videoStatus.status !== 'completed') {
      const error = createErrorDetails(new Error('Video is not ready for download'), 'download')
      setState(prev => ({ ...prev, error }))
      throw new Error('Video is not ready for download')
    }

    try {
      const blob = await withRetry(
        () => apiService.downloadVideo(state.videoId!),
        'Video Download',
        -1 // Special index for download step
      )

      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${state.tutorialPlan?.title || 'tutorial'}.mp4`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error) {
      const errorDetails = createErrorDetails(error, 'download')
      setState(prev => ({ ...prev, error: errorDetails }))
      console.error('Failed to download video:', error)
      throw error
    }
  }, [state.videoId, state.tutorialPlan?.title, state.videoStatus, withRetry, createErrorDetails])

  // Retry the current operation
  const retryCurrentOperation = useCallback(async (problem?: string) => {
    if (!state.error || !state.error.retryable) {
      throw new Error('Current error is not retryable')
    }

    if (state.error.step === 'download') {
      return downloadVideo()
    }

    if (problem) {
      return generateTutorial(problem)
    }

    throw new Error('Cannot retry: no problem provided for tutorial generation')
  }, [state.error, downloadVideo, generateTutorial])

  // Clear error state
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  // Cleanup function to call on unmount
  const cleanup = useCallback(() => {
    isMountedRef.current = false
  }, [])

  // Effect cleanup
  React.useEffect(() => {
    return cleanup
  }, [cleanup])

  return {
    // State
    ...state,

    // Actions
    generateTutorial,
    downloadVideo,
    resetState,
    retryCurrentOperation,
    clearError,

    // Computed values
    isCompleted: state.steps.every(step => step.status === 'completed'),
    hasError: state.error !== null,
    isRetryable: state.error?.retryable ?? false,
    completedSteps: state.steps.filter(step => step.status === 'completed').length,
    totalSteps: state.steps.length,
    progressPercentage: (state.steps.filter(step => step.status === 'completed').length / state.steps.length) * 100,

    // Error details
    errorType: state.error?.type,
    errorMessage: state.error?.message,
    errorStep: state.error?.step,
    canRetry: state.error?.retryable && state.retryCount < state.maxRetries,

    // Progress details
    currentStepName: state.steps[state.currentStep]?.title,
    hasStepsWithErrors: state.steps.some(step => step.status === 'error')
  }
}

export default useTutorialGeneration
