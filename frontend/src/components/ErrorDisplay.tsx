import React from 'react'

interface ErrorDetails {
  message: string
  type: 'network' | 'timeout' | 'api' | 'validation' | 'unknown'
  code?: string
  step?: string
  retryable: boolean
  timestamp: Date
}

interface ErrorDisplayProps {
  error: ErrorDetails
  onRetry?: () => void
  onDismiss?: () => void
  isRetrying?: boolean
  canRetry?: boolean
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onRetry,
  onDismiss,
  isRetrying = false,
  canRetry = false
}) => {
  const getErrorIcon = () => {
    switch (error.type) {
      case 'network':
        return (
          <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        )
      case 'timeout':
        return (
          <svg className="w-6 h-6 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      case 'validation':
        return (
          <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      default:
        return (
          <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        )
    }
  }

  const getErrorTitle = () => {
    switch (error.type) {
      case 'network':
        return 'Connection Error'
      case 'timeout':
        return 'Request Timeout'
      case 'api':
        return 'Server Error'
      case 'validation':
        return 'Input Error'
      default:
        return 'Unexpected Error'
    }
  }

  const getErrorDescription = () => {
    switch (error.type) {
      case 'network':
        return 'Unable to connect to the server. Please check your internet connection.'
      case 'timeout':
        return 'The request took too long to complete. The server might be busy.'
      case 'api':
        return 'The server encountered an error while processing your request.'
      case 'validation':
        return 'Please check your input and try again.'
      default:
        return 'An unexpected error occurred. Please try again.'
    }
  }

  const getBorderColor = () => {
    switch (error.type) {
      case 'network':
        return 'border-red-200'
      case 'timeout':
        return 'border-orange-200'
      case 'validation':
        return 'border-yellow-200'
      default:
        return 'border-red-200'
    }
  }

  const getBackgroundColor = () => {
    switch (error.type) {
      case 'network':
        return 'bg-red-50'
      case 'timeout':
        return 'bg-orange-50'
      case 'validation':
        return 'bg-yellow-50'
      default:
        return 'bg-red-50'
    }
  }

  return (
    <div className={`rounded-lg border ${getBorderColor()} ${getBackgroundColor()} p-4`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {getErrorIcon()}
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-gray-900">
            {getErrorTitle()}
            {error.step && (
              <span className="text-gray-600 font-normal"> - {error.step}</span>
            )}
          </h3>
          <div className="mt-2 text-sm text-gray-700">
            <p>{error.message}</p>
            {error.type !== 'validation' && (
              <p className="mt-1 text-gray-600">{getErrorDescription()}</p>
            )}
            {error.code && (
              <p className="mt-1 text-xs text-gray-500">Error Code: {error.code}</p>
            )}
          </div>
          <div className="mt-3 flex space-x-3">
            {canRetry && onRetry && (
              <button
                onClick={onRetry}
                disabled={isRetrying}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isRetrying ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Retrying...
                  </>
                ) : (
                  'Retry'
                )}
              </button>
            )}
            {onDismiss && (
              <button
                onClick={onDismiss}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Dismiss
              </button>
            )}
          </div>
        </div>
        {onDismiss && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                onClick={onDismiss}
                className="inline-flex rounded-md p-1.5 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <span className="sr-only">Dismiss</span>
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
      <div className="mt-2 text-xs text-gray-500">
        {error.timestamp.toLocaleString()}
      </div>
    </div>
  )
}

export default ErrorDisplay
