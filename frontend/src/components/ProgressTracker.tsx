import React from 'react'

interface ProgressStep {
  id: string
  title: string
  status: 'pending' | 'in-progress' | 'completed' | 'error'
  description?: string
  error?: string
}

interface ProgressTrackerProps {
  steps?: ProgressStep[]
  currentStep?: number
}

const defaultSteps: ProgressStep[] = [
  {
    id: '1',
    title: 'Analyzing Problem',
    status: 'pending',
    description: 'AI is understanding your coding challenge'
  },
  {
    id: '2',
    title: 'Generating Steps',
    status: 'pending',
    description: 'Creating step-by-step solution plan'
  },
  {
    id: '3',
    title: 'Creating Animation',
    status: 'pending',
    description: 'Building animated tutorial video'
  },
  {
    id: '4',
    title: 'Rendering Video',
    status: 'pending',
    description: 'Finalizing your tutorial'
  }
]

const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  steps = defaultSteps
}) => {
  const getStepIcon = (status: ProgressStep['status']) => {
    switch (status) {
      case 'completed':
        return (
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
        )
      case 'in-progress':
        return (
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
          </div>
        )
      case 'error':
        return (
          <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
        )
      default:
        return (
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
          </div>
        )
    }
  }

  const getStepTextColor = (status: ProgressStep['status']) => {
    switch (status) {
      case 'completed':
        return 'text-green-700'
      case 'in-progress':
        return 'text-blue-700'
      case 'error':
        return 'text-red-700'
      default:
        return 'text-gray-500'
    }
  }

  const hasActiveSteps = steps.some(step => 
    step.status === 'in-progress' || step.status === 'completed'
  )

  if (!hasActiveSteps) {
    return null
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Generation Progress
      </h3>
      
      <div className="space-y-4">
        {steps.map((step) => (
          <div key={step.id} className="flex items-start space-x-3">
            {getStepIcon(step.status)}
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h4 className={`text-sm font-medium ${getStepTextColor(step.status)}`}>
                  {step.title}
                </h4>
                {step.status === 'in-progress' && (
                  <div className="flex space-x-1">
                    <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce"></div>
                    <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                )}
              </div>
              {step.description && (
                <p className="text-xs text-gray-500 mt-1">
                  {step.description}
                </p>
              )}
              {step.status === 'error' && step.error && (
                <p className="text-xs text-red-600 mt-1 bg-red-50 px-2 py-1 rounded">
                  Error: {step.error}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Progress Bar */}
      <div className="mt-6">
        <div className="flex justify-between text-xs text-gray-500 mb-2">
          <span>Progress</span>
          <span>
            {steps.filter(s => s.status === 'completed').length} of {steps.length} complete
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ 
              width: `${(steps.filter(s => s.status === 'completed').length / steps.length) * 100}%` 
            }}
          ></div>
        </div>
      </div>
    </div>
  )
}

export default ProgressTracker
