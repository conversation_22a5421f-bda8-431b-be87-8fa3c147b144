import React from 'react';
import {
  AbsoluteFill,
  interpolate,
  spring,
  useCurrentFrame,
  useVideoConfig,
} from 'remotion';

interface TutorialStep {
  stepNumber: number;
  title: string;
  description: string;
  codeExample?: string;
  uiActions: string[];
  expectedResult: string;
}

interface TutorialVideoProps {
  title: string;
  steps: TutorialStep[];
  metadata?: {
    difficulty: string;
    estimatedTime: string;
    technologies: string[];
  };
}

export const TutorialVideo: React.FC<TutorialVideoProps> = ({ title, steps, metadata }) => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();

  // Timing configuration
  const introDuration = 4 * fps; // 4 seconds intro
  const stepDuration = 12 * fps; // 12 seconds per step for better readability

  // Determine current phase
  const isIntro = frame < introDuration;
  const stepFrame = frame - introDuration;
  const currentStepIndex = Math.floor(stepFrame / stepDuration);
  const currentStep = steps[currentStepIndex] || steps[0];
  const stepLocalFrame = stepFrame % stepDuration;

  // Animation values
  const titleOpacity = interpolate(frame, [0, 60], [0, 1], {
    extrapolateRight: 'clamp',
  });

  const introScale = spring({
    frame,
    fps,
    config: {
      damping: 200,
      stiffness: 100,
    },
  });

  const stepProgress = spring({
    frame: stepLocalFrame,
    fps,
    config: {
      damping: 200,
      stiffness: 100,
    },
  });

  // Enhanced animations for different elements
  const codeOpacity = interpolate(stepLocalFrame, [fps * 2, fps * 3], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const actionsOpacity = interpolate(stepLocalFrame, [fps * 4, fps * 5], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const resultOpacity = interpolate(stepLocalFrame, [fps * 6, fps * 7], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // Show intro or step content
  const showIntro = isIntro;
  const showStep = !isIntro && currentStepIndex < steps.length;

  return (
    <AbsoluteFill style={{ backgroundColor: '#f8fafc' }}>
      {/* Intro Phase */}
      {showIntro && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            transform: `scale(${introScale})`,
            opacity: titleOpacity,
          }}
        >
          <h1
            style={{
              fontSize: 72,
              fontWeight: 'bold',
              color: '#1e293b',
              margin: 0,
              textAlign: 'center',
              marginBottom: 40,
            }}
          >
            {title}
          </h1>

          {metadata && (
            <div style={{ textAlign: 'center', color: '#64748b' }}>
              <p style={{ fontSize: 24, margin: '10px 0' }}>
                Difficulty: <span style={{ color: '#3b82f6', fontWeight: 'bold' }}>{metadata.difficulty}</span>
              </p>
              <p style={{ fontSize: 24, margin: '10px 0' }}>
                Estimated Time: <span style={{ color: '#10b981', fontWeight: 'bold' }}>{metadata.estimatedTime}</span>
              </p>
              <p style={{ fontSize: 20, margin: '10px 0' }}>
                Technologies: {metadata.technologies.join(', ')}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Step Content Phase */}
      {showStep && (
        <>
          {/* Header */}
          <div
            style={{
              position: 'absolute',
              top: 40,
              left: 60,
              right: 60,
              opacity: titleOpacity,
            }}
          >
            <h1
              style={{
                fontSize: 36,
                fontWeight: 'bold',
                color: '#1e293b',
                margin: 0,
                textAlign: 'center',
              }}
            >
              {title}
            </h1>
          </div>

          {/* Step Content */}
          <div
            style={{
              position: 'absolute',
              top: 140,
              left: 60,
              right: 60,
              bottom: 60,
              transform: `translateY(${(1 - stepProgress) * 20}px)`,
              opacity: stepProgress,
            }}
          >
        {/* Step Number and Title */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: 40,
          }}
        >
          <div
            style={{
              width: 60,
              height: 60,
              borderRadius: '50%',
              backgroundColor: '#3b82f6',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: 24,
              fontWeight: 'bold',
              marginRight: 20,
            }}
          >
            {currentStep.stepNumber}
          </div>
          <h2
            style={{
              fontSize: 36,
              fontWeight: 'bold',
              color: '#1e293b',
              margin: 0,
            }}
          >
            {currentStep.title}
          </h2>
        </div>

        {/* Description */}
        <div
          style={{
            backgroundColor: 'white',
            padding: 30,
            borderRadius: 12,
            marginBottom: 30,
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          }}
        >
          <p
            style={{
              fontSize: 24,
              color: '#475569',
              margin: 0,
              lineHeight: 1.6,
            }}
          >
            {currentStep.description}
          </p>
        </div>

        {/* Code Example */}
        {currentStep.codeExample && (
          <div
            style={{
              backgroundColor: '#1e293b',
              padding: 30,
              borderRadius: 12,
              marginBottom: 30,
              fontFamily: 'Monaco, Consolas, monospace',
              opacity: codeOpacity,
              transform: `translateY(${(1 - codeOpacity) * 10}px)`,
              border: '2px solid #3b82f6',
            }}
          >
            <div
              style={{
                color: '#3b82f6',
                fontSize: 14,
                marginBottom: 10,
                fontWeight: 'bold',
              }}
            >
              💻 Code Example:
            </div>
            <pre
              style={{
                color: '#e2e8f0',
                fontSize: 18,
                margin: 0,
                whiteSpace: 'pre-wrap',
              }}
            >
              {currentStep.codeExample}
            </pre>
          </div>
        )}

        {/* UI Actions */}
        <div
          style={{
            backgroundColor: '#f1f5f9',
            padding: 30,
            borderRadius: 12,
            marginBottom: 30,
            opacity: actionsOpacity,
            transform: `translateY(${(1 - actionsOpacity) * 10}px)`,
            border: '2px solid #f59e0b',
          }}
        >
          <h3
            style={{
              fontSize: 20,
              fontWeight: 'bold',
              color: '#1e293b',
              marginTop: 0,
              marginBottom: 15,
            }}
          >
            🎯 Actions to take:
          </h3>
          <ul style={{ margin: 0, paddingLeft: 20 }}>
            {currentStep.uiActions.map((action, index) => (
              <li
                key={index}
                style={{
                  fontSize: 18,
                  color: '#475569',
                  marginBottom: 8,
                  listStyleType: '▶️',
                }}
              >
                {action}
              </li>
            ))}
          </ul>
        </div>

        {/* Expected Result */}
        <div
          style={{
            backgroundColor: '#dcfce7',
            padding: 30,
            borderRadius: 12,
            border: '2px solid #16a34a',
            opacity: resultOpacity,
            transform: `translateY(${(1 - resultOpacity) * 10}px)`,
          }}
        >
          <h3
            style={{
              fontSize: 20,
              fontWeight: 'bold',
              color: '#15803d',
              marginTop: 0,
              marginBottom: 15,
            }}
          >
            ✅ Expected Result:
          </h3>
          <p
            style={{
              fontSize: 18,
              color: '#166534',
              margin: 0,
            }}
          >
            {currentStep.expectedResult}
          </p>
        </div>
          </div>
        </>
      )}

      {/* Progress Indicator */}
      <div
        style={{
          position: 'absolute',
          bottom: 30,
          left: 60,
          right: 60,
          height: 6,
          backgroundColor: '#e2e8f0',
          borderRadius: 3,
        }}
      >
        <div
          style={{
            height: '100%',
            backgroundColor: '#3b82f6',
            borderRadius: 3,
            width: showIntro
              ? `${(frame / introDuration) * 10}%` // 10% for intro
              : `${10 + ((currentStepIndex + stepProgress) / steps.length) * 90}%`, // 90% for steps
            transition: 'width 0.3s ease',
          }}
        />
      </div>

      {/* Step Counter */}
      {showStep && (
        <div
          style={{
            position: 'absolute',
            bottom: 50,
            right: 60,
            color: '#64748b',
            fontSize: 18,
            fontWeight: 'bold',
          }}
        >
          Step {currentStepIndex + 1} of {steps.length}
        </div>
      )}
    </AbsoluteFill>
  );
};
